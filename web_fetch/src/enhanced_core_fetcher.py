"""
Enhanced core fetcher with support for all resource types.

This module extends the base WebFetcher with enhanced content parsing
capabilities for PDFs, images, feeds, CSV files, and more.
"""

from __future__ import annotations

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import aiohttp
from bs4 import BeautifulSoup

from ..exceptions import WebFetchError, ContentError
from ..models.base import ContentType
from ..models.http import FetchConfig, FetchRequest, FetchResult, BatchFetchRequest, BatchFetchResult
from ..utils.response import ResponseAnalyzer
from ..utils.content_detector import ContentTypeDetector
from ..utils.error_handler import <PERSON>hanced<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RetryConfig
from ..utils.advanced_rate_limiter import AdvancedRateLimiter, RateLimitConfig
from ..utils.enhanced_cache import EnhancedCache, CacheConfig
from ..utils.js_renderer import Java<PERSON><PERSON><PERSON>er, JSRenderConfig
from ..parsers import EnhancedContentParser
from .core_fetcher import WebFetcher

logger = logging.getLogger(__name__)


class EnhancedWebFetcher(WebFetcher):
    """Enhanced web fetcher with support for all resource types."""
    
    def __init__(
        self,
        config: Optional[FetchConfig] = None,
        cache_config: Optional[CacheConfig] = None,
        js_config: Optional[JSRenderConfig] = None
    ):
        """
        Initialize enhanced web fetcher.

        Args:
            config: Optional FetchConfig object for customizing behavior
            cache_config: Optional CacheConfig for caching behavior
            js_config: Optional JSRenderConfig for JavaScript rendering
        """
        super().__init__(config)
        self._content_parser = EnhancedContentParser()
        self._content_detector = ContentTypeDetector()
        self._error_handler = EnhancedErrorHandler()
        self._advanced_rate_limiter = AdvancedRateLimiter()
        self._cache = EnhancedCache(cache_config) if cache_config else None
        self._js_renderer = JavaScriptRenderer(js_config) if js_config else None
    
    async def _execute_request(self, request: FetchRequest, attempt: int) -> FetchResult:
        """
        Execute a single HTTP request with enhanced content parsing.
        
        This method extends the base implementation to support enhanced
        content parsing for all resource types.
        
        Args:
            request: FetchRequest object containing URL and options
            attempt: Current attempt number (0-based)
            
        Returns:
            FetchResult with enhanced metadata and parsed content
        """
        if not self._session:
            raise WebFetchError("Session not initialized")

        # Check cache first
        if self._cache:
            cached_result = await self._cache.get(str(request.url), request.headers)
            if cached_result:
                logger.debug(f"Cache hit for {request.url}")
                return cached_result
        
        # Prepare request parameters
        request_kwargs = {
            'method': request.method,
            'headers': request.headers or {},
            'timeout': aiohttp.ClientTimeout(
                total=request.timeout_override or self.config.total_timeout,
                connect=self.config.connect_timeout
            )
        }
        
        # Add request data if present
        if request.data:
            if isinstance(request.data, dict):
                request_kwargs['json'] = request.data
            else:
                request_kwargs['data'] = request.data
        
        # Add query parameters
        if request.params:
            request_kwargs['params'] = request.params

        # Apply advanced rate limiting
        try:
            delay = await self._advanced_rate_limiter.acquire(str(request.url))
            if delay > 0:
                logger.debug(f"Rate limiting delay: {delay:.2f}s for {request.url}")
                await asyncio.sleep(delay)
        except Exception as e:
            logger.warning(f"Rate limiting failed for {request.url}: {e}")

        # Record request start time for response recording
        request_start_time = time.time()

        try:
            async with self._session.request(str(request.url), **request_kwargs) as response:
                # Read response content
                content_bytes = await response.read()
                
                # Create base result
                result = FetchResult(
                    url=str(request.url),
                    status_code=response.status,
                    headers=dict(response.headers),
                    content_type=request.content_type,
                    timestamp=datetime.now()
                )
                
                # Parse content with enhanced parser
                parsed_content, enhanced_result = await self._content_parser.parse_content(
                    content_bytes=content_bytes,
                    requested_type=request.content_type,
                    url=str(request.url),
                    headers=dict(response.headers)
                )
                
                # Merge enhanced result metadata into main result
                result.content = parsed_content
                result.pdf_metadata = enhanced_result.pdf_metadata
                result.image_metadata = enhanced_result.image_metadata
                result.feed_metadata = enhanced_result.feed_metadata
                result.feed_items = enhanced_result.feed_items
                result.csv_metadata = enhanced_result.csv_metadata
                result.links = enhanced_result.links
                result.content_summary = enhanced_result.content_summary
                result.extracted_text = enhanced_result.extracted_text
                result.structured_data = enhanced_result.structured_data
                
                # Copy any parsing errors
                if enhanced_result.error:
                    result.error = enhanced_result.error

                # Record response for rate limiting
                response_time = time.time() - request_start_time
                try:
                    await self._advanced_rate_limiter.record_response(
                        str(request.url),
                        response.status,
                        dict(response.headers),
                        response_time
                    )
                except Exception as e:
                    logger.warning(f"Failed to record response for rate limiting: {e}")

                # Cache successful results
                if self._cache and result.is_success:
                    try:
                        await self._cache.set(str(request.url), result, request.headers)
                    except Exception as e:
                        logger.warning(f"Failed to cache result: {e}")

                return result
                
        except aiohttp.ClientError as e:
            # Use enhanced error handling
            error_info = self._error_handler.categorize_error(
                exception=e,
                url=str(request.url)
            )

            # Record error for statistics
            self._error_handler.record_error(str(request.url), error_info)

            error_msg = f"HTTP client error: {e}"
            logger.error(f"Request to {request.url} failed: {error_msg}")

            return FetchResult(
                url=str(request.url),
                status_code=0,
                headers={},
                content=None,
                content_type=request.content_type,
                timestamp=datetime.now(),
                error=error_msg,
                retry_count=attempt
            )
        
        except asyncio.TimeoutError as e:
            # Use enhanced error handling for timeouts
            error_info = self._error_handler.categorize_error(
                exception=e,
                url=str(request.url)
            )

            # Record error for statistics
            self._error_handler.record_error(str(request.url), error_info)

            error_msg = "Request timeout"
            logger.error(f"Request to {request.url} timed out")

            return FetchResult(
                url=str(request.url),
                status_code=0,
                headers={},
                content=None,
                content_type=request.content_type,
                timestamp=datetime.now(),
                error=error_msg,
                retry_count=attempt
            )
        
        except Exception as e:
            # Handle unexpected errors
            error_msg = f"Unexpected error: {e}"
            logger.error(f"Unexpected error for {request.url}: {error_msg}")
            
            return FetchResult(
                url=str(request.url),
                status_code=0,
                headers={},
                content=None,
                content_type=request.content_type,
                timestamp=datetime.now(),
                error=error_msg,
                retry_count=attempt
            )
    
    async def fetch_with_auto_detection(self, url: str, headers: Optional[Dict[str, str]] = None) -> FetchResult:
        """
        Fetch URL with automatic content type detection.
        
        This method first fetches the content, detects its type, then re-parses
        it with the appropriate parser for optimal results.
        
        Args:
            url: URL to fetch
            headers: Optional custom headers
            
        Returns:
            FetchResult with automatically detected and parsed content
        """
        # First fetch with RAW content type to get headers and content
        raw_request = FetchRequest(
            url=url,
            headers=headers,
            content_type=ContentType.RAW
        )
        
        raw_result = await self.fetch_single(raw_request)
        
        if not raw_result.is_success or not raw_result.content:
            return raw_result
        
        # Use intelligent content type detection
        if isinstance(raw_result.content, bytes):
            best_content_type, confidence = self._content_detector.detect_content_type(
                content=raw_result.content,
                url=url,
                headers=raw_result.headers
            )

            logger.debug(f"Detected content type: {best_content_type} (confidence: {confidence:.2f})")
        else:
            best_content_type = ContentType.TEXT
            confidence = 0.1
        
        # If we detected a different type, re-parse with the correct parser
        if best_content_type != ContentType.RAW and isinstance(raw_result.content, bytes):
            try:
                parsed_content, enhanced_result = await self._content_parser.parse_content(
                    content_bytes=raw_result.content,
                    requested_type=best_content_type,
                    url=url,
                    headers=raw_result.headers
                )
                
                # Update the result with enhanced parsing
                raw_result.content = parsed_content
                raw_result.content_type = best_content_type
                raw_result.pdf_metadata = enhanced_result.pdf_metadata
                raw_result.image_metadata = enhanced_result.image_metadata
                raw_result.feed_metadata = enhanced_result.feed_metadata
                raw_result.feed_items = enhanced_result.feed_items
                raw_result.csv_metadata = enhanced_result.csv_metadata
                raw_result.links = enhanced_result.links
                raw_result.content_summary = enhanced_result.content_summary
                raw_result.extracted_text = enhanced_result.extracted_text
                raw_result.structured_data = enhanced_result.structured_data
                
            except Exception as e:
                logger.warning(f"Enhanced parsing failed for {url}, using raw content: {e}")
        
        return raw_result

    def get_content_detection_info(
        self,
        content: bytes,
        url: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
        filename: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get detailed content type detection information.

        Args:
            content: Content bytes to analyze
            url: Optional URL for context
            headers: Optional HTTP headers
            filename: Optional filename

        Returns:
            Dictionary with detection details and confidence scores
        """
        return self._content_detector.get_detection_info(content, url, headers, filename)

    def get_error_statistics(self, domain: Optional[str] = None) -> Dict[str, Any]:
        """
        Get error statistics for analysis.

        Args:
            domain: Optional domain to get statistics for (None for all domains)

        Returns:
            Dictionary with error statistics
        """
        return self._error_handler.get_error_statistics(domain)

    def get_rate_limit_status(self, domain: Optional[str] = None) -> Dict[str, Any]:
        """
        Get rate limiting status for analysis.

        Args:
            domain: Optional domain to get status for (None for all domains)

        Returns:
            Dictionary with rate limiting status
        """
        return self._advanced_rate_limiter.get_rate_limit_status(domain)

    async def get_cache_statistics(self) -> Dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        if self._cache:
            return await self._cache.get_statistics()
        else:
            return {'error': 'Cache not enabled'}

    async def clear_cache(self) -> bool:
        """
        Clear all cached content.

        Returns:
            True if cache was cleared successfully
        """
        if self._cache:
            return await self._cache.clear()
        else:
            return False

    async def close(self) -> None:
        """Close the enhanced fetcher and cleanup resources."""
        await super().close()

        if self._cache:
            await self._cache.close()

        if self._js_renderer:
            await self._js_renderer.close()
    
    def get_supported_content_types(self) -> List[ContentType]:
        """
        Get list of all supported content types.
        
        Returns:
            List of supported ContentType enum values
        """
        return [
            ContentType.RAW,
            ContentType.TEXT,
            ContentType.JSON,
            ContentType.HTML,
            ContentType.PDF,
            ContentType.IMAGE,
            ContentType.RSS,
            ContentType.CSV,
            ContentType.MARKDOWN,
            ContentType.XML,
        ]
    
    def get_parser_info(self) -> Dict[str, Any]:
        """
        Get information about available parsers and their capabilities.
        
        Returns:
            Dictionary with parser information
        """
        return {
            'pdf_parser': {
                'available': hasattr(self._content_parser, 'pdf_parser'),
                'features': ['text_extraction', 'metadata', 'page_count', 'encryption_detection']
            },
            'image_parser': {
                'available': hasattr(self._content_parser, 'image_parser'),
                'features': ['exif_data', 'dimensions', 'format_detection', 'thumbnail_generation']
            },
            'feed_parser': {
                'available': hasattr(self._content_parser, 'feed_parser'),
                'features': ['rss_parsing', 'atom_parsing', 'item_extraction', 'metadata']
            },
            'csv_parser': {
                'available': hasattr(self._content_parser, 'csv_parser'),
                'features': ['delimiter_detection', 'type_inference', 'encoding_detection', 'pandas_integration']
            },
            'markdown_converter': {
                'available': hasattr(self._content_parser, 'markdown_converter'),
                'features': ['html_to_markdown', 'table_conversion', 'link_preservation', 'code_block_handling']
            }
        }

    async def fetch_js_rendered(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        **js_options: Any
    ) -> FetchResult:
        """
        Fetch JavaScript-rendered content.

        Args:
            url: URL to fetch
            headers: Optional HTTP headers
            **js_options: Additional JavaScript rendering options

        Returns:
            FetchResult with rendered content

        Raises:
            WebFetchError: If JavaScript rendering is not available or fails
        """
        if not self._js_renderer:
            raise WebFetchError("JavaScript rendering not enabled. Initialize with js_config parameter.")

        try:
            # Render the page
            render_result = await self._js_renderer.render_page(url, **js_options)

            # Create FetchResult from rendered data
            result = FetchResult(
                url=render_result.get('final_url', url),
                status_code=render_result.get('status_code', 200),
                headers=render_result.get('headers', {}),
                content=render_result.get('html_content', ''),
                content_type=ContentType.HTML,
                timestamp=datetime.now()
            )

            # Add JavaScript-specific metadata to structured_data
            js_metadata = {}

            if 'text_content' in render_result:
                result.extracted_text = render_result['text_content']
                js_metadata['text_content'] = render_result['text_content']

            if 'title' in render_result:
                js_metadata['title'] = render_result['title']

            if 'links' in render_result:
                result.links = render_result['links']
                js_metadata['links'] = render_result['links']

            if 'images' in render_result:
                js_metadata['images'] = render_result['images']

            if 'meta_tags' in render_result:
                js_metadata['meta_tags'] = render_result['meta_tags']

            if 'metrics' in render_result:
                js_metadata['performance_metrics'] = render_result['metrics']

            # Store JavaScript rendering metadata
            js_metadata['rendered_with_js'] = True
            js_metadata['session_id'] = render_result.get('session_id')

            if not result.structured_data:
                result.structured_data = {}
            result.structured_data['js_rendering'] = js_metadata

            # Cache the result if caching is enabled
            if self._cache:
                try:
                    await self._cache.set(url, result, headers)
                except Exception as e:
                    logger.warning(f"Failed to cache JS-rendered result: {e}")

            return result

        except Exception as e:
            logger.error(f"JavaScript rendering failed for {url}: {e}")
            raise WebFetchError(f"JavaScript rendering failed: {e}")

    def is_js_rendering_available(self) -> bool:
        """Check if JavaScript rendering is available."""
        return self._js_renderer is not None and self._js_renderer.is_available()

    async def take_screenshot(
        self,
        url: str,
        path: Optional[str] = None,
        **screenshot_options: Any
    ) -> bytes:
        """
        Take a screenshot of a webpage.

        Args:
            url: URL to screenshot
            path: Optional file path to save screenshot
            **screenshot_options: Additional screenshot options

        Returns:
            Screenshot as bytes

        Raises:
            WebFetchError: If JavaScript rendering is not available
        """
        if not self._js_renderer:
            raise WebFetchError("JavaScript rendering not enabled. Initialize with js_config parameter.")

        return await self._js_renderer.screenshot(url, path, **screenshot_options)
