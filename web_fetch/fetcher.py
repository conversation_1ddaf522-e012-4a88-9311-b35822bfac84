"""
Core async web fetcher implementation using AIOHTTP.

This module provides the main WebFetcher class that handles asynchronous HTTP requests
with proper session management, connection pooling, and modern Python features.

This is the main entry point that imports and re-exports all functionality from
the modular components in the src/ directory for backward compatibility.
"""

from __future__ import annotations

# Import all components from the modular structure
from .src.core_fetcher import WebFetcher
from .src.streaming_fetcher import StreamingWebFetcher
from .src.convenience import (
    fetch_url,
    fetch_urls,
    download_file,
    fetch_with_cache,
)
from .src.url_utils import (
    is_valid_url,
    normalize_url,
    analyze_url,
    analyze_headers,
    detect_content_type,
)

# Re-export all functionality for backward compatibility
__all__ = [
    # Core classes
    "WebFetcher",
    "StreamingWebFetcher",

    # Convenience functions
    "fetch_url",
    "fetch_urls",
    "download_file",
    "fetch_with_cache",

    # URL utilities
    "is_valid_url",
    "normalize_url",
    "analyze_url",
    "analyze_headers",
    "detect_content_type",
]