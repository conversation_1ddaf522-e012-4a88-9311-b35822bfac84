"""
Enhanced web fetcher with advanced features.

This module demonstrates how to integrate the new enhancements like circuit breakers,
request deduplication, response transformation, and metrics collection into the
existing web fetcher functionality.
"""

from __future__ import annotations

import asyncio
import time
from typing import Any, Dict, List, Optional, Union

from pydantic import HttpUrl

from .models.http import FetchConfig, FetchRequest, FetchResult
from .models.base import ContentType
from .src.core_fetcher import WebFetcher
from .utils.circuit_breaker import CircuitBreakerConfig, with_circuit_breaker
from .utils.deduplication import deduplicate_request, RequestKey
from .utils.transformers import TransformationPipeline, Transformer
from .utils.metrics import record_request_metrics


class EnhancedWebFetcher:
    """
    Enhanced web fetcher with circuit breaker, deduplication, transformation, and metrics.
    
    This class wraps the standard WebFetcher with additional resilience and monitoring
    features for production use.
    """
    
    def __init__(self, 
                 config: Optional[FetchConfig] = None,
                 circuit_breaker_config: Optional[CircuitBreakerConfig] = None,
                 enable_deduplication: bool = True,
                 enable_metrics: bool = True,
                 transformation_pipeline: Optional[TransformationPipeline] = None):
        """
        Initialize enhanced web fetcher.
        
        Args:
            config: Standard fetch configuration
            circuit_breaker_config: Circuit breaker configuration
            enable_deduplication: Whether to enable request deduplication
            enable_metrics: Whether to collect metrics
            transformation_pipeline: Optional response transformation pipeline
        """
        self.config = config or FetchConfig()
        self.circuit_breaker_config = circuit_breaker_config or CircuitBreakerConfig()
        self.enable_deduplication = enable_deduplication
        self.enable_metrics = enable_metrics
        self.transformation_pipeline = transformation_pipeline
        
        self._base_fetcher: Optional[WebFetcher] = None
    
    async def __aenter__(self) -> EnhancedWebFetcher:
        """Async context manager entry."""
        self._base_fetcher = WebFetcher(self.config)
        await self._base_fetcher.__aenter__()
        return self
    
    async def __aexit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Async context manager exit."""
        if self._base_fetcher:
            await self._base_fetcher.__aexit__(exc_type, exc_val, exc_tb)
    
    async def fetch_single(self, request: FetchRequest) -> FetchResult:
        """
        Fetch a single URL with all enhancements.
        
        Args:
            request: Fetch request to process
            
        Returns:
            Enhanced fetch result with metrics and transformations applied
        """
        start_time = time.time()
        
        try:
            if self.enable_deduplication:
                # Use request deduplication
                result = await self._fetch_with_deduplication(request)
            else:
                # Direct fetch with circuit breaker
                result = await self._fetch_with_circuit_breaker(request)
            
            # Apply response transformation if configured
            if self.transformation_pipeline and result.content is not None:
                result = await self._apply_transformations(result)
            
            # Record metrics
            if self.enable_metrics:
                response_time = time.time() - start_time
                record_request_metrics(
                    url=str(request.url),
                    method=request.method,
                    status_code=result.status_code,
                    response_time=response_time,
                    response_size=len(str(result.content)) if result.content else 0,
                    error=result.error
                )
            
            return result
            
        except Exception as e:
            # Record error metrics
            if self.enable_metrics:
                response_time = time.time() - start_time
                record_request_metrics(
                    url=str(request.url),
                    method=request.method,
                    status_code=0,
                    response_time=response_time,
                    error=str(e)
                )
            raise
    
    async def _fetch_with_deduplication(self, request: FetchRequest) -> FetchResult:
        """Fetch with request deduplication."""
        request_key = RequestKey(
            url=str(request.url),
            method=request.method,
            headers=request.headers,
            data=request.data,
            params=request.params
        )
        
        return await deduplicate_request(
            url=str(request.url),
            method=request.method,
            headers=request.headers,
            data=request.data,
            params=request.params,
            executor_func=self._fetch_with_circuit_breaker,
            request=request
        )
    
    async def _fetch_with_circuit_breaker(self, request: FetchRequest) -> FetchResult:
        """Fetch with circuit breaker protection."""
        return await with_circuit_breaker(
            url=str(request.url),
            func=self._base_fetcher.fetch_single,
            config=self.circuit_breaker_config,
            request=request
        )
    
    async def _apply_transformations(self, result: FetchResult) -> FetchResult:
        """Apply response transformations."""
        if not self.transformation_pipeline:
            return result
        
        try:
            context = {
                'url': result.url,
                'headers': result.headers,
                'status_code': result.status_code
            }
            
            transformation_result = await self.transformation_pipeline.transform(
                result.content, context
            )
            
            if transformation_result.is_success:
                # Create new result with transformed content
                transformed_result = FetchResult(
                    url=result.url,
                    status_code=result.status_code,
                    headers=result.headers,
                    content=transformation_result.data,
                    content_type=result.content_type,
                    response_time=result.response_time,
                    timestamp=result.timestamp,
                    error=result.error,
                    retry_count=result.retry_count
                )
                
                # Add transformation metadata to headers
                if transformation_result.metadata:
                    transformed_result.headers['X-Transformation-Metadata'] = str(transformation_result.metadata)
                
                return transformed_result
            else:
                # Transformation failed, return original with error info
                result.error = f"Transformation failed: {'; '.join(transformation_result.errors)}"
                return result
                
        except Exception as e:
            # Transformation error, return original result with error
            result.error = f"Transformation error: {str(e)}"
            return result
    
    async def fetch_batch(self, requests: List[FetchRequest]) -> List[FetchResult]:
        """
        Fetch multiple URLs with all enhancements.
        
        Args:
            requests: List of fetch requests
            
        Returns:
            List of enhanced fetch results
        """
        # Use semaphore to control concurrency
        semaphore = asyncio.Semaphore(self.config.max_concurrent_requests)
        
        async def fetch_with_semaphore(request: FetchRequest) -> FetchResult:
            async with semaphore:
                return await self.fetch_single(request)
        
        # Execute all requests concurrently
        tasks = [fetch_with_semaphore(request) for request in requests]
        return await asyncio.gather(*tasks, return_exceptions=False)


# Convenience functions for enhanced fetching

async def enhanced_fetch_url(url: str, 
                           method: str = "GET",
                           headers: Optional[Dict[str, str]] = None,
                           data: Optional[Union[str, bytes, Dict[str, Any]]] = None,
                           params: Optional[Dict[str, str]] = None,
                           content_type: ContentType = ContentType.RAW,
                           config: Optional[FetchConfig] = None,
                           circuit_breaker_config: Optional[CircuitBreakerConfig] = None,
                           enable_deduplication: bool = True,
                           enable_metrics: bool = True,
                           transformation_pipeline: Optional[TransformationPipeline] = None) -> FetchResult:
    """
    Convenience function for enhanced single URL fetching.
    
    Args:
        url: URL to fetch
        method: HTTP method
        headers: Request headers
        data: Request data
        params: Query parameters
        content_type: Expected content type
        config: Fetch configuration
        circuit_breaker_config: Circuit breaker configuration
        enable_deduplication: Enable request deduplication
        enable_metrics: Enable metrics collection
        transformation_pipeline: Response transformation pipeline
        
    Returns:
        Enhanced fetch result
    """
    request = FetchRequest(
        url=HttpUrl(url),
        method=method,
        headers=headers,
        data=data,
        params=params,
        content_type=content_type
    )
    
    async with EnhancedWebFetcher(
        config=config,
        circuit_breaker_config=circuit_breaker_config,
        enable_deduplication=enable_deduplication,
        enable_metrics=enable_metrics,
        transformation_pipeline=transformation_pipeline
    ) as fetcher:
        return await fetcher.fetch_single(request)


async def enhanced_fetch_urls(urls: List[str],
                            method: str = "GET",
                            headers: Optional[Dict[str, str]] = None,
                            content_type: ContentType = ContentType.RAW,
                            config: Optional[FetchConfig] = None,
                            circuit_breaker_config: Optional[CircuitBreakerConfig] = None,
                            enable_deduplication: bool = True,
                            enable_metrics: bool = True,
                            transformation_pipeline: Optional[TransformationPipeline] = None) -> List[FetchResult]:
    """
    Convenience function for enhanced batch URL fetching.
    
    Args:
        urls: List of URLs to fetch
        method: HTTP method for all requests
        headers: Request headers for all requests
        content_type: Expected content type
        config: Fetch configuration
        circuit_breaker_config: Circuit breaker configuration
        enable_deduplication: Enable request deduplication
        enable_metrics: Enable metrics collection
        transformation_pipeline: Response transformation pipeline
        
    Returns:
        List of enhanced fetch results
    """
    requests = [
        FetchRequest(
            url=HttpUrl(url),
            method=method,
            headers=headers,
            content_type=content_type
        )
        for url in urls
    ]
    
    async with EnhancedWebFetcher(
        config=config,
        circuit_breaker_config=circuit_breaker_config,
        enable_deduplication=enable_deduplication,
        enable_metrics=enable_metrics,
        transformation_pipeline=transformation_pipeline
    ) as fetcher:
        return await fetcher.fetch_batch(requests)


__all__ = [
    "EnhancedWebFetcher",
    "enhanced_fetch_url",
    "enhanced_fetch_urls",
]
