"""
Enhanced content caching system with multiple backends and intelligent features.

This module provides intelligent caching with TTL, ETag support, conditional requests,
cache invalidation, and multiple storage backends including memory, file, and Redis.
"""

from __future__ import annotations

import asyncio
import hashlib
import json
import logging
import os
import pickle
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple
from urllib.parse import urlparse

try:
    import redis.asyncio as redis
    HAS_REDIS = True
except ImportError:
    HAS_REDIS = False

from ..models.http import FetchResult

logger = logging.getLogger(__name__)


class CacheBackend(Enum):
    """Cache backend types."""
    
    MEMORY = "memory"
    FILE = "file"
    REDIS = "redis"


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    
    key: str
    data: Any
    timestamp: float
    ttl: Optional[float] = None
    etag: Optional[str] = None
    last_modified: Optional[str] = None
    content_type: Optional[str] = None
    size: int = 0
    hit_count: int = 0
    last_accessed: float = field(default_factory=time.time)
    
    @property
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        if self.ttl is None:
            return False
        return time.time() - self.timestamp > self.ttl
    
    @property
    def age(self) -> float:
        """Get age of cache entry in seconds."""
        return time.time() - self.timestamp


@dataclass
class CacheConfig:
    """Configuration for enhanced cache."""
    
    backend: CacheBackend = CacheBackend.MEMORY
    default_ttl: float = 3600.0  # 1 hour
    max_size: int = 1000  # Maximum number of entries
    max_memory_mb: int = 100  # Maximum memory usage in MB
    enable_etag: bool = True
    enable_conditional_requests: bool = True
    enable_compression: bool = True
    file_cache_dir: Optional[str] = None
    redis_url: Optional[str] = None
    redis_prefix: str = "web_fetch:"
    cleanup_interval: float = 300.0  # 5 minutes


class CacheBackendInterface(ABC):
    """Abstract interface for cache backends."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get cache entry by key."""
        pass
    
    @abstractmethod
    async def set(self, entry: CacheEntry) -> bool:
        """Set cache entry."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete cache entry by key."""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """Clear all cache entries."""
        pass
    
    @abstractmethod
    async def keys(self) -> List[str]:
        """Get all cache keys."""
        pass
    
    @abstractmethod
    async def size(self) -> int:
        """Get number of cache entries."""
        pass


class MemoryCacheBackend(CacheBackendInterface):
    """In-memory cache backend."""
    
    def __init__(self, config: CacheConfig):
        """Initialize memory cache backend."""
        self.config = config
        self.cache: Dict[str, CacheEntry] = {}
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get cache entry by key."""
        async with self._lock:
            entry = self.cache.get(key)
            if entry and not entry.is_expired:
                entry.hit_count += 1
                entry.last_accessed = time.time()
                return entry
            elif entry and entry.is_expired:
                # Remove expired entry
                del self.cache[key]
            return None
    
    async def set(self, entry: CacheEntry) -> bool:
        """Set cache entry."""
        async with self._lock:
            # Check size limits
            if len(self.cache) >= self.config.max_size:
                await self._evict_entries()
            
            self.cache[entry.key] = entry
            return True
    
    async def delete(self, key: str) -> bool:
        """Delete cache entry by key."""
        async with self._lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    async def clear(self) -> bool:
        """Clear all cache entries."""
        async with self._lock:
            self.cache.clear()
            return True
    
    async def keys(self) -> List[str]:
        """Get all cache keys."""
        async with self._lock:
            return list(self.cache.keys())
    
    async def size(self) -> int:
        """Get number of cache entries."""
        async with self._lock:
            return len(self.cache)
    
    async def _evict_entries(self):
        """Evict entries using LRU strategy."""
        if not self.cache:
            return
        
        # Sort by last accessed time and remove oldest
        sorted_entries = sorted(
            self.cache.items(),
            key=lambda x: x[1].last_accessed
        )
        
        # Remove 10% of entries
        num_to_remove = max(1, len(sorted_entries) // 10)
        for key, _ in sorted_entries[:num_to_remove]:
            del self.cache[key]


class FileCacheBackend(CacheBackendInterface):
    """File-based cache backend."""
    
    def __init__(self, config: CacheConfig):
        """Initialize file cache backend."""
        self.config = config
        self.cache_dir = Path(config.file_cache_dir or "cache")
        self.cache_dir.mkdir(exist_ok=True)
        self._lock = asyncio.Lock()
    
    def _get_file_path(self, key: str) -> Path:
        """Get file path for cache key."""
        # Create safe filename from key
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{safe_key}.cache"
    
    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get cache entry by key."""
        file_path = self._get_file_path(key)
        
        if not file_path.exists():
            return None
        
        try:
            async with self._lock:
                with open(file_path, 'rb') as f:
                    entry = pickle.load(f)
                
                if not entry.is_expired:
                    entry.hit_count += 1
                    entry.last_accessed = time.time()
                    
                    # Update file with new access time
                    with open(file_path, 'wb') as f:
                        pickle.dump(entry, f)
                    
                    return entry
                else:
                    # Remove expired file
                    file_path.unlink(missing_ok=True)
                    return None
        
        except Exception as e:
            logger.warning(f"Failed to read cache file {file_path}: {e}")
            file_path.unlink(missing_ok=True)
            return None
    
    async def set(self, entry: CacheEntry) -> bool:
        """Set cache entry."""
        file_path = self._get_file_path(entry.key)
        
        try:
            async with self._lock:
                with open(file_path, 'wb') as f:
                    pickle.dump(entry, f)
                return True
        
        except Exception as e:
            logger.error(f"Failed to write cache file {file_path}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete cache entry by key."""
        file_path = self._get_file_path(key)
        
        try:
            file_path.unlink(missing_ok=True)
            return True
        except Exception as e:
            logger.warning(f"Failed to delete cache file {file_path}: {e}")
            return False
    
    async def clear(self) -> bool:
        """Clear all cache entries."""
        try:
            for file_path in self.cache_dir.glob("*.cache"):
                file_path.unlink(missing_ok=True)
            return True
        except Exception as e:
            logger.error(f"Failed to clear cache directory: {e}")
            return False
    
    async def keys(self) -> List[str]:
        """Get all cache keys."""
        keys = []
        for file_path in self.cache_dir.glob("*.cache"):
            try:
                with open(file_path, 'rb') as f:
                    entry = pickle.load(f)
                    if not entry.is_expired:
                        keys.append(entry.key)
                    else:
                        # Remove expired file
                        file_path.unlink(missing_ok=True)
            except Exception:
                # Remove corrupted file
                file_path.unlink(missing_ok=True)
        
        return keys
    
    async def size(self) -> int:
        """Get number of cache entries."""
        return len(await self.keys())


class RedisCacheBackend(CacheBackendInterface):
    """Redis cache backend."""
    
    def __init__(self, config: CacheConfig):
        """Initialize Redis cache backend."""
        if not HAS_REDIS:
            raise ImportError("redis is required for Redis cache backend")
        
        self.config = config
        self.redis_url = config.redis_url or "redis://localhost:6379"
        self.prefix = config.redis_prefix
        self.redis_client = None
    
    async def _get_client(self):
        """Get Redis client, creating if necessary."""
        if self.redis_client is None:
            self.redis_client = redis.from_url(self.redis_url)
        return self.redis_client
    
    def _make_key(self, key: str) -> str:
        """Create Redis key with prefix."""
        return f"{self.prefix}{key}"
    
    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get cache entry by key."""
        try:
            client = await self._get_client()
            redis_key = self._make_key(key)
            
            data = await client.get(redis_key)
            if data:
                entry = pickle.loads(data)
                if not entry.is_expired:
                    entry.hit_count += 1
                    entry.last_accessed = time.time()
                    
                    # Update entry in Redis
                    await client.set(redis_key, pickle.dumps(entry))
                    
                    return entry
                else:
                    # Remove expired entry
                    await client.delete(redis_key)
            
            return None
        
        except Exception as e:
            logger.warning(f"Redis get failed for key {key}: {e}")
            return None
    
    async def set(self, entry: CacheEntry) -> bool:
        """Set cache entry."""
        try:
            client = await self._get_client()
            redis_key = self._make_key(entry.key)
            
            data = pickle.dumps(entry)
            
            if entry.ttl:
                await client.setex(redis_key, int(entry.ttl), data)
            else:
                await client.set(redis_key, data)
            
            return True
        
        except Exception as e:
            logger.error(f"Redis set failed for key {entry.key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete cache entry by key."""
        try:
            client = await self._get_client()
            redis_key = self._make_key(key)
            result = await client.delete(redis_key)
            return result > 0
        
        except Exception as e:
            logger.warning(f"Redis delete failed for key {key}: {e}")
            return False
    
    async def clear(self) -> bool:
        """Clear all cache entries."""
        try:
            client = await self._get_client()
            pattern = f"{self.prefix}*"
            keys = await client.keys(pattern)
            
            if keys:
                await client.delete(*keys)
            
            return True
        
        except Exception as e:
            logger.error(f"Redis clear failed: {e}")
            return False
    
    async def keys(self) -> List[str]:
        """Get all cache keys."""
        try:
            client = await self._get_client()
            pattern = f"{self.prefix}*"
            redis_keys = await client.keys(pattern)
            
            # Remove prefix from keys
            prefix_len = len(self.prefix)
            return [key.decode()[prefix_len:] for key in redis_keys]
        
        except Exception as e:
            logger.warning(f"Redis keys failed: {e}")
            return []
    
    async def size(self) -> int:
        """Get number of cache entries."""
        keys = await self.keys()
        return len(keys)


class EnhancedCache:
    """Enhanced cache with multiple backends and intelligent features."""
    
    def __init__(self, config: Optional[CacheConfig] = None):
        """
        Initialize enhanced cache.
        
        Args:
            config: Cache configuration
        """
        self.config = config or CacheConfig()
        self.backend = self._create_backend()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0,
        }
        
        # Start cleanup task
        self._cleanup_task = None
        if self.config.cleanup_interval > 0:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    def _create_backend(self) -> CacheBackendInterface:
        """Create cache backend based on configuration."""
        if self.config.backend == CacheBackend.MEMORY:
            return MemoryCacheBackend(self.config)
        elif self.config.backend == CacheBackend.FILE:
            return FileCacheBackend(self.config)
        elif self.config.backend == CacheBackend.REDIS:
            return RedisCacheBackend(self.config)
        else:
            raise ValueError(f"Unsupported cache backend: {self.config.backend}")
    
    def _generate_cache_key(self, url: str, headers: Optional[Dict[str, str]] = None) -> str:
        """Generate cache key for URL and headers."""
        # Normalize URL
        parsed = urlparse(url)
        normalized_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        if parsed.query:
            normalized_url += f"?{parsed.query}"
        
        # Include relevant headers in key
        key_parts = [normalized_url]
        
        if headers:
            # Include headers that affect content
            relevant_headers = ['accept', 'accept-language', 'accept-encoding', 'user-agent']
            for header in relevant_headers:
                if header in headers:
                    key_parts.append(f"{header}:{headers[header]}")
        
        key_string = "|".join(key_parts)
        return hashlib.sha256(key_string.encode()).hexdigest()
    
    async def get(self, url: str, headers: Optional[Dict[str, str]] = None) -> Optional[FetchResult]:
        """
        Get cached result for URL.
        
        Args:
            url: URL to get cached result for
            headers: Optional request headers
            
        Returns:
            Cached FetchResult if available and valid
        """
        cache_key = self._generate_cache_key(url, headers)
        
        try:
            entry = await self.backend.get(cache_key)
            
            if entry:
                self.stats['hits'] += 1
                logger.debug(f"Cache hit for {url}")
                return entry.data
            else:
                self.stats['misses'] += 1
                logger.debug(f"Cache miss for {url}")
                return None
        
        except Exception as e:
            logger.warning(f"Cache get failed for {url}: {e}")
            self.stats['misses'] += 1
            return None
    
    async def set(
        self,
        url: str,
        result: FetchResult,
        headers: Optional[Dict[str, str]] = None,
        ttl: Optional[float] = None
    ) -> bool:
        """
        Cache result for URL.
        
        Args:
            url: URL to cache result for
            result: FetchResult to cache
            headers: Optional request headers
            ttl: Optional TTL override
            
        Returns:
            True if cached successfully
        """
        cache_key = self._generate_cache_key(url, headers)
        
        # Determine TTL
        cache_ttl = ttl or self.config.default_ttl
        
        # Extract cache-related headers from result
        etag = result.headers.get('etag')
        last_modified = result.headers.get('last-modified')
        content_type = result.headers.get('content-type')
        
        # Calculate size estimate
        size = 0
        if result.content:
            if isinstance(result.content, (str, bytes)):
                size = len(result.content)
            else:
                size = len(str(result.content))
        
        # Create cache entry
        entry = CacheEntry(
            key=cache_key,
            data=result,
            timestamp=time.time(),
            ttl=cache_ttl,
            etag=etag,
            last_modified=last_modified,
            content_type=content_type,
            size=size
        )
        
        try:
            success = await self.backend.set(entry)
            if success:
                self.stats['sets'] += 1
                logger.debug(f"Cached result for {url}")
            return success
        
        except Exception as e:
            logger.warning(f"Cache set failed for {url}: {e}")
            return False
    
    async def delete(self, url: str, headers: Optional[Dict[str, str]] = None) -> bool:
        """
        Delete cached result for URL.
        
        Args:
            url: URL to delete cached result for
            headers: Optional request headers
            
        Returns:
            True if deleted successfully
        """
        cache_key = self._generate_cache_key(url, headers)
        
        try:
            success = await self.backend.delete(cache_key)
            if success:
                self.stats['deletes'] += 1
            return success
        
        except Exception as e:
            logger.warning(f"Cache delete failed for {url}: {e}")
            return False
    
    async def clear(self) -> bool:
        """Clear all cached results."""
        try:
            success = await self.backend.clear()
            if success:
                self.stats = {key: 0 for key in self.stats}
            return success
        
        except Exception as e:
            logger.error(f"Cache clear failed: {e}")
            return False
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            size = await self.backend.size()
            keys = await self.backend.keys()
            
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0.0
            
            return {
                'backend': self.config.backend.value,
                'size': size,
                'hit_rate': hit_rate,
                'stats': self.stats.copy(),
                'config': {
                    'default_ttl': self.config.default_ttl,
                    'max_size': self.config.max_size,
                    'enable_etag': self.config.enable_etag,
                    'enable_conditional_requests': self.config.enable_conditional_requests,
                }
            }
        
        except Exception as e:
            logger.warning(f"Failed to get cache statistics: {e}")
            return {'error': str(e)}
    
    async def _cleanup_loop(self):
        """Periodic cleanup of expired entries."""
        while True:
            try:
                await asyncio.sleep(self.config.cleanup_interval)
                await self._cleanup_expired()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"Cache cleanup failed: {e}")
    
    async def _cleanup_expired(self):
        """Clean up expired cache entries."""
        try:
            keys = await self.backend.keys()
            expired_count = 0
            
            for key in keys:
                entry = await self.backend.get(key)
                if entry and entry.is_expired:
                    await self.backend.delete(key)
                    expired_count += 1
            
            if expired_count > 0:
                logger.debug(f"Cleaned up {expired_count} expired cache entries")
                self.stats['evictions'] += expired_count
        
        except Exception as e:
            logger.warning(f"Cache cleanup failed: {e}")
    
    async def close(self):
        """Close cache and cleanup resources."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Close Redis connection if using Redis backend
        if isinstance(self.backend, RedisCacheBackend) and self.backend.redis_client:
            await self.backend.redis_client.close()
