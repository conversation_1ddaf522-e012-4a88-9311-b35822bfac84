"""
Cache utility module for the web_fetch library.

This module provides caching functionality for HTTP responses with TTL and LRU eviction.
"""

from __future__ import annotations

import gzip
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from ..models import CacheConfig, CacheEntry


class SimpleCache:
    """
    Simple in-memory cache for HTTP responses.

    Implements an LRU (Least Recently Used) cache with TTL (Time To Live)
    expiration for HTTP responses. Supports optional compression and
    automatic cleanup of expired entries.
    """

    def __init__(self, config: CacheConfig):
        """
        Initialize cache with configuration.

        Args:
            config: CacheConfig object specifying cache behavior including
                   max_size, TTL, compression settings, and header caching.

        Attributes:
            config: The cache configuration
            _cache: Internal storage for cache entries
            _access_order: List tracking access order for LRU eviction
        """
        self.config = config
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order: List[str] = []
    
    def _cleanup_expired(self) -> None:
        """
        Remove expired entries from cache.

        Scans all cache entries and removes those that have exceeded
        their TTL. Called automatically during get operations.
        """
        expired_keys = [
            key for key, entry in self._cache.items()
            if entry.is_expired
        ]
        for key in expired_keys:
            self._remove_entry(key)

    def _remove_entry(self, key: str) -> None:
        """
        Remove entry from cache and access order.

        Args:
            key: Cache key to remove
        """
        if key in self._cache:
            del self._cache[key]
        if key in self._access_order:
            self._access_order.remove(key)

    def _evict_lru(self) -> None:
        """
        Evict least recently used entries if cache is full.

        Removes the oldest accessed entries until cache size is
        below the configured maximum. Uses LRU eviction policy.
        """
        while len(self._cache) >= self.config.max_size and self._access_order:
            lru_key = self._access_order.pop(0)
            self._remove_entry(lru_key)
    
    def get(self, url: str) -> Optional[CacheEntry]:
        """
        Get cached entry for URL.
        
        Args:
            url: URL to look up
            
        Returns:
            CacheEntry if found and not expired, None otherwise
        """
        self._cleanup_expired()
        
        if url not in self._cache:
            return None
        
        entry = self._cache[url]
        if entry.is_expired:
            self._remove_entry(url)
            return None
        
        # Update access order
        if url in self._access_order:
            self._access_order.remove(url)
        self._access_order.append(url)
        
        return entry
    
    def put(self, url: str, response_data: Any, headers: Dict[str, str], status_code: int) -> None:
        """
        Store response in cache.
        
        Args:
            url: URL to cache
            response_data: Response data to cache
            headers: Response headers
            status_code: HTTP status code
        """
        self._cleanup_expired()
        self._evict_lru()
        
        # Compress data if enabled
        compressed = False
        if self.config.enable_compression and isinstance(response_data, (str, bytes)):
            try:
                if isinstance(response_data, str):
                    response_data = response_data.encode('utf-8')
                response_data = gzip.compress(response_data)
                compressed = True
            except Exception:
                pass  # Fall back to uncompressed
        
        entry = CacheEntry(
            url=url,
            response_data=response_data,
            headers=headers if self.config.cache_headers else {},
            status_code=status_code,
            timestamp=datetime.now(),
            ttl=timedelta(seconds=self.config.ttl_seconds),
            compressed=compressed
        )
        
        self._cache[url] = entry
        if url in self._access_order:
            self._access_order.remove(url)
        self._access_order.append(url)
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self._cache.clear()
        self._access_order.clear()
    
    def size(self) -> int:
        """Get current cache size."""
        return len(self._cache)
